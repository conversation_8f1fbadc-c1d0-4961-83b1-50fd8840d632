import { useState, useEffect } from 'react'
import axios from 'axios'
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import './core/env.ts'

import './App.scss'
import { ReactView } from './view/render/ReactView.tsx';
import { BladeView } from './view/render/BladeView.tsx';
import { AbsoluteSection } from './ndesignUI/container/AbsoluteSection.tsx';

import type { BaseSchema } from 'nui';
import Loading from './components/Loading.tsx';
import { BladeRender } from './core/render/BladeRender.ts';
import DemoWasm from './demo/demoWasm.tsx';

const examleSchema: BaseSchema = {
  component: 'div',
  id: '1',
  // template:`<?php 
  // $bladeString = '@if($data["isAdmin"]) <p>管理员</p> @else <p>普通用户</p> @endif';
  // #$bladeString = '<div> @customFunction("hello") </div>';
  // $stringOutput = renderBladeString($bladeString, ['isAdmin' => false]);
  // $temp = getBladeCode($bladeString);
  // echo "template===$temp";

  // echo $stringOutput;
  // ?>`
  template: `@if($data["isAdmin"]) <p>管理员</p> @else <p>普通用户</p> @endif`
}
const buttonSchema = {
  component: 'div',
  id: '1',
  style: [
    'color:red'
  ],
  template: `
  <ul>
  @for ($i = 1; $i <= count($data["array"]); $i++)
      @if ($i % 2 === 0)
          <li>{{ $i }}</li>
      @else
        <li>{{ $i }}</li>
      @endif
  @endfor
</ul>
  `
}


const absoluteSectionSchema: BaseSchema = {
  component: 'AbsoluteSection',
  id: 'absolute-section-demo',
  name: '绝对定位容器演示',
  body: [{
    component: 'button',
    id: 'btn1',
    name: '按钮1'
  },
  {
    component: 'button',
    id: 'btn2',
    name: '按钮2'
  }

  ]
}
function DemoComp() {
  const [scheme, setSchema] = useState({})

  useEffect(() => {
    (async function () {
      let render = BladeRender.getInstance();
      await render.defineVariable("isAdmin", "0")
      await render.defineVariable("object", '{"a":1,"b":2}')
      await render.defineVariable("array", [{ "a": 1, "b": 2 }, { "a": 1, "b": 2 }])

      // todo
      //await render.definedServiceVariable("array",{api:'',params})

      setSchema(examleSchema);
      setSchema(buttonSchema);
    })()
  }, [])



  return (
    <>
      <BladeView schema={scheme}></BladeView>
    </>
  )
}

export default DemoComp
