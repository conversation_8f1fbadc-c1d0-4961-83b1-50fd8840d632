export type classType = string | Record<string, any>

export type styleType = string | Record<string, any>

export type expressionType = string

export type SchemaBody = {}

export type ComponentProperty = {
  name: string,
  value?: any
}

export interface BaseSchema {
  component: string,
  id: string
  name?: string
  class?: classType
  style?: styleType
  slots?: Array<string>
  template?: string
  body?: BaseSchema[] | Record<string, BaseSchema> | string;
  props?: any
}

{
    "branchId": "00000000-0000-0000-0000-000000000000",
    "atomicTransactions": [
        {
            "transaction": {
                "correlationId": "82ia33s5gt50-158",
                "actions": [
                    {
                        "op": "REPLACE",
                        "id": "masterPage",
                        "namespace": "fixerVersions",
                        "value": {
                            "migration_fixer": "AgMBAQEBAQEBAQEBAQICAgECAwEIAQECAQEBAAIBAAEAAQEBAQEEAQAIAQAAAQEBAQEBAQIAAgIBAAAEAwEA",
                            "id": "masterPage",
                            "viewer_fixer": "AQEBAQEBAgEBAQECAgEBAQEBAQIBAQEBAQECAQEBAQEBAQMBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQECAQEBAQEBAQEBAQEBAQEBAQECAQEBAQEDAQEBAQEBAQEBAQEBAQEBAQEBAgECAQAAAAABAQEAAAAAAAAAAA==",
                            "type": "FixerVersions",
                            "metaData": {
                                "schemaVersion": "1.0",
                                "isPreset": false,
                                "isHidden": false,
                                "pageId": "masterPage",
                                "sig": "nie-88"
                            }
                        },
                        "basedOnSignature": "nie-40",
                        "sig": "nie-88"
                    }
                ],
                "metadata": {
                    "siteVersion": "2",
                    "initiator": {
                        "initiatorType": "DM_CLIENT"
                    }
                }
            }
        }
    ]
}