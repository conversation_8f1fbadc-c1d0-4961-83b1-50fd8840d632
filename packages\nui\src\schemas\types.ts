export type classType = string | Record<string, any>

export type styleType = string | Record<string, any>

export type expressionType = string

export type SchemaBody = {}

export type ComponentProperty = {
  name: string,
  value?: any
}

export interface BaseSchema {
  component: string,
  id: string
  name?: string
  class?: classType
  style?: styleType
  slots?: Array<string>
  template?: string
  body?: BaseSchema[] | Record<string, BaseSchema> | string;
  props?: any
}