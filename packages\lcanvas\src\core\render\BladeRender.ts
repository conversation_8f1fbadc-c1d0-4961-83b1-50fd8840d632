import { getDefinedComponent, type BaseSchema } from "nui";
import { throttle } from "../utils";
import type { IRender } from "./IRender";
import parse from 'html-react-parser';

export const VarScope = {
  /**
   * 跨应用
   */
  SESSION: 'session',
  /**
   * 全局变量，跨页面，跨组件
   */
  APPLICATION: 'global',
  /**
   * 页面级变量，跨组件
   */
  PAGE: 'data',
  /**
   * 组件级
   */
  COMPONENT: 'state'
} as const

export type ScopeType = typeof VarScope[keyof typeof VarScope];

/**
 * 变量作用域
 */
export type VariableScope = {
  scope: ScopeType,
  /** 
   * 作用域路径 
   * 如 scope = 'page'时，路径一般为: ".page_112233"，生成的PHP页面变量为:$["data"]["page_112233"]
   * 如 scope = 'state'时，路径一般为: ".page_112233.comp_112233"，生成的PHP页面变量为:$["data"]["page_112233"]["comp_112233"]
   */
  path: string;
}

export class BladeRender implements IRender {
  static isWASMReady: boolean = false;
  static instance: BladeRender
  php: any = null

  // 变量
  defineVariableLater: Array<{
    name: string,
    value: any,
    scope: VariableScope
  }> = [];

  
  onBladeOutput: any = null;
  constructor() {

  }
  static getInstance() {
    if (!BladeRender.instance) {
      BladeRender.instance = new BladeRender();
    }
    return BladeRender.instance;
  }
  async loadWASM() {
    console.log('load...')
    const host = `${window.location.protocol}//${window.location.host}`;
    console.log('host', host);
    const phpModule = await import(`${host}/lib/blade/PhpWeb.mjs`);
    this.php = new phpModule.PhpWeb();
    const phpBinary = await this.php.binary;
    const zipResponse = await fetch(`${host}/lib/blade/blade-template.zip`);
    if (!zipResponse.ok) {
      throw new Error('无法下载 ZIP 文件: ' + zipResponse.status);
    }
    const zipData = await zipResponse.arrayBuffer();
    phpBinary.FS.writeFile('./blade-template.zip', new Uint8Array(zipData));
    if (!phpBinary.FS.analyzePath('./workspace').exists) {
      phpBinary.FS.mkdir('./workspace');
    }
    await this.php.run(`<?php
                     
                    $zip = new ZipArchive;
                    echo "zip....";

                    $reflection = new ReflectionClass('ZipArchive');
                    echo "类名：" . $reflection->getName() . "\\n";

                    $result = $zip->open("./blade-template.zip");
                    if ($result === TRUE) {
                        if (!$zip->extractTo("./workspace/")) {
                            exit(1);
                        }
                        $zip->close();
                    } else {
                        exit(1);
                    }
                    echo "after extract....";
                ?>`);
    await this.php.run(`<?php include_once './workspace/blade-template/index.php'; ?>`);
    BladeRender.isWASMReady = true;

    // define later
    for(let i=0;i<this.defineVariableLater.length;i++){
      let item = this.defineVariableLater[i];
      await this.createBladeVariable(item.name, item.value, item.scope);
    }
    this.defineVariableLater = [];
  }

  /**
   * 
   * @param variableName 
   * @param variableValue 
   * @param scope 默认为page，即前启动的wasm页面会话
   * @returns 
   */
  async createBladeVariable(variableName: string, variableValue: any, scope: VariableScope = { scope: 'global', path: '' }) {
    let value = variableValue;
    let isObject = false;
    if (typeof variableValue == 'object') {
      isObject = true;
    } else {
      try {
        let obj = JSON.parse(variableValue);
        value = obj;
        isObject = true;
      } catch (err) { }
    }
    if (isObject) {
      value = `json_decode(\'${JSON.stringify(value)}\')`;
    }


    let code = `<?php echo "";
    if( !isset($page) ){
      $page=["data"=>[]];
    }
    if (!array_key_exists('data', $page) || !is_array($page['data'])) {
      $page['data'] = [];
    }
    `

    let key = `$data["${variableName}"]`
    switch (scope.scope) {
      case VarScope.SESSION:
        key = `$_SESSION['${variableName}']`
        code += `
          session_start();
          if (!isset(${key})) {
            ${key}= null;
          }
          ${key} = ${JSON.stringify(value)};
          session_end();
        `;
        break;
      default:
        code += `
          
          if (!isset($data)) {
              $data = $page["data"];

          }
          ${key} = ${value};
          $page["data"] = $data;
          echo '${variableName} is defined ';
          `
        break;
    }
    code += `
    var_dump($page);
    ?>`;
    //code = `<?php echo 'abc'; ?>`
    console.log('code===', code)
    return code;
  }

  async createBladeServiceVariable(variableName: string, serviceDefine: string, scope = 'page') {

  }

  /**
   * 渲染Blade 模板 或者执行PHP代码
   * @param phpstr 
   * @param blade 是否是Blade模板，默认是
   * @returns 
   */

  async executePhp(phpstr: string, blade: boolean = true): Promise<string> {
    console.log("blade", phpstr)
    let code = `<?php 
    $bladeString = '${phpstr}';
    $stringOutput = renderBladeString($bladeString, $page);
    echo $stringOutput;
    ?>`;

    if (!blade) {
      code = phpstr;
    }
    console.log('code===', code)

    return new Promise(async (resolve, reject) => {
      let php = this.php;
      let res: string[] = [];
      let timeoutId: NodeJS.Timeout | null = null;
      const onBladeOutput = (e: any) => {
        // console.log('php----', e.detail, new Date().valueOf());
        res = res.concat(e.detail || []);
        if (timeoutId) {
          clearTimeout(timeoutId);
        }
        timeoutId = setTimeout(() => {
          if (res.length) {
            if (timeoutId) clearTimeout(timeoutId)
            resolve(res.join(''));
          }

        }, 100);
      };

      this.onBladeOutput = onBladeOutput;
      if (BladeRender.isWASMReady) {
        php.addEventListener('output', this.onBladeOutput);
        //php.onoutput = this.onBladeOutput;
        await php.run(code);
        //resetTimer();
      } else {
        await this.loadWASM();
        php = this.php;
        php.addEventListener('output', this.onBladeOutput);
        //code = `<?php echo 'abc'; ?>`
        await php.run(code);
        // resetTimer();
      }
    })
  }

  async prepare() {
    //await this.definedVariable('test','1')
  }
  async defineVariable(name: string, value: any) {
    if( !BladeRender.isWASMReady){
      this.defineVariableLater.push({
        name:name,
        value:value,
        scope:{scope:VarScope.SESSION, path:''}
      })
      return;
    }
    const code = await this.createBladeVariable(name, value);
    await this.executePhp(code, false)
  }


  mergeDefinedSchema (schema:BaseSchema ){
    const definedSchema = getDefinedComponent(schema.component);
    if( !definedSchema ) return schema;
    const newSchema = {...schema}
    if(definedSchema.props){
      newSchema.props = definedSchema.props.map((item:any)=>{
        return {
          ...item,
          value: schema.props?.[item.name]
        }
      })
    }
    return newSchema;
  }

  async render(schema: BaseSchema) {
    await this.prepare();

    const renderTemplate = async (schema: BaseSchema) => {
      let html = '';
      if (schema.template) {
        html = await this.executePhp(schema.template);
      }
      const hasSlot = schema.template?.indexOf('<slot>')!=-1
      let parserOption = {
        replace: (dom:any)=>{
          
        }
      }
      if( Array.isArray(schema.body) && hasSlot){
        let childTemplate = ''
        for (const item of schema.body) {
          const merged = this.mergeDefinedSchema(item)
          if( !merged.template) continue;
          const itemHtml = await this.executePhp(merged.template || '');
          childTemplate += itemHtml;
        }
        parserOption.replace = (dom:any)=>{
          if( dom && dom.name=='slot'){
            return parse(`<div>${childTemplate}</div>`)
          } 
        }
      }
      return parse(html, parserOption);;
    }
    // const html = await this.executePhp(schema.template ?? '')
    // console.log('html', html)
    // const ele = parse(html, {
    //   replace: (dom:any) => {
        
    //   }
    // });
    // return ele;
    return renderTemplate(schema)
  }

  createElement = (element: BaseSchema) => {

  }
}