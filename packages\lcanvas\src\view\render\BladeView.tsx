import React, { useRef, useEffect } from 'react'
import { createRoot, type Container } from 'react-dom/client';
import type { SchemaRenderProps } from '../../types';


import { BladeRender } from '../../core/render/BladeRender';
import './index.scss'
export const BladeView = (props: SchemaRenderProps) => {
  
  const root = useRef(null);
  
  const render = async ( schema:any )=>{
    if( !schema || !schema.template){
      console.error('不可用的数据')
      return;
    }
    if (root.current) {
      console.log('渲染组件')
      // 创建 root 实例
      const rootInstance = createRoot(root.current);
      const ele = await (BladeRender.getInstance()).render(schema) as any;
      // 使用 root 实例的 render 方法渲染组件
      let dom = rootInstance.render([ele]);
    }
  }

  const renderComponent = ( root:Container )=>{

  }
  useEffect(() => {
    const run = async () => {
      if( props.schema ){
        render(props.schema)
      }
    }
    run()
  },[props.schema])


  return <>
    <div ref={root}>loading schema...</div>
  </>


}