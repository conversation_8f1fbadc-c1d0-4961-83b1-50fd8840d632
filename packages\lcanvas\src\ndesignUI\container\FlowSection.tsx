import React, { useState, useRef, useCallback } from 'react'
import type { BaseSchema } from 'nui'

export type FlowSectionProps = {
  schema?: BaseSchema
  width?: number | string
  height?: number | string
  direction?: 'row' | 'column'
  wrap?: boolean
  gap?: number
  rowGap?: number
  columnGap?: number
  justify?: 'flex-start' | 'center' | 'flex-end' | 'space-between' | 'space-around' | 'space-evenly'
  align?: 'flex-start' | 'center' | 'flex-end' | 'stretch'
  onSchemaChange?: (schema: BaseSchema) => void
}

export const FlowSection = (props: FlowSectionProps) => {
  const {
    schema,
    width = '100%',
    height = '400px',
    direction = 'row',
    wrap = true,
    gap = 10,
    rowGap,
    columnGap,
    justify = 'flex-start',
    align = 'flex-start',
    onSchemaChange
  } = props

  // 计算实际的行间距和列间距
  const actualRowGap = rowGap !== undefined ? rowGap : (direction === 'row' ? Math.max(4, gap * 0.4) : gap)
  const actualColumnGap = columnGap !== undefined ? columnGap : (direction === 'row' ? gap : Math.max(4, gap * 0.4))

  // 初始化子组件数据
  const initializeChildren = () => {
    if (schema?.body && typeof schema.body === 'object' && !Array.isArray(schema.body)) {
      const bodyRecord = schema.body as Record<string, BaseSchema>
      return Object.keys(bodyRecord).map(key => {
        const childSchema = bodyRecord[key]
        return {
          id: childSchema.id,
          schema: childSchema
        }
      })
    }
    // 默认示例数据
    return [
      {
        id: 'flow-btn1',
        schema: {
          component: 'button',
          id: 'flow-btn1',
          name: '按钮1'
        } as BaseSchema
      },
      {
        id: 'flow-text1',
        schema: {
          component: 'text',
          id: 'flow-text1',
          name: '文本内容'
        } as BaseSchema
      }
    ]
  }

  const [children, setChildren] = useState(initializeChildren)
  const [dragOverIndex, setDragOverIndex] = useState<number | null>(null)
  const [draggedIndex, setDraggedIndex] = useState<number | null>(null)
  const [isDragging, setIsDragging] = useState(false)
  const [dropPosition, setDropPosition] = useState<'left' | 'right' | null>(null)
  const [insertIndex, setInsertIndex] = useState<number | null>(null)
  const containerRef = useRef<HTMLDivElement>(null)

  // 监听 schema 变化，同步更新 children
  React.useEffect(() => {
    if (schema?.body && typeof schema.body === 'object' && !Array.isArray(schema.body)) {
      const bodyRecord = schema.body as Record<string, BaseSchema>
      setChildren(prevChildren => {
        const schemaKeys = Object.keys(bodyRecord)
        const currentKeys = prevChildren.map(child => child.id)

        // 检查是否有变化
        if (schemaKeys.length !== currentKeys.length ||
            !schemaKeys.every(key => currentKeys.includes(bodyRecord[key].id))) {

          const newChildren = schemaKeys.map(key => {
            const childSchema = bodyRecord[key]
            return {
              id: childSchema.id,
              schema: childSchema
            }
          })
          return newChildren
        }
        return prevChildren
      })
    } else {
      // 如果没有 body，清空 children
      setChildren([])
    }
  }, [schema])



  // 渲染子组件
  const renderChild = (childSchema: BaseSchema) => {
    switch (childSchema.component) {
      case 'button':
        return (
          <button
            style={{
              padding: '8px 16px',
              backgroundColor: '#007bff',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              cursor: 'pointer',
              minWidth: '80px'
            }}
          >
            {childSchema.name || '按钮'}
          </button>
        )
      case 'text':
        return (
          <div
            style={{
              padding: '8px 12px',
              backgroundColor: '#f8f9fa',
              border: '1px solid #dee2e6',
              borderRadius: '4px',
              minWidth: '60px',
              color: '#333'
            }}
          >
            {childSchema.name || '文本'}
          </div>
        )
      case 'div':
        return (
          <div
            style={{
              padding: '10px',
              backgroundColor: '#f8f9fa',
              border: '1px solid #dee2e6',
              borderRadius: '4px',
              minWidth: '80px',
              minHeight: '40px'
            }}
          >
            {childSchema.name || 'Div容器'}
          </div>
        )
      case 'FlowSection':
        // 支持嵌套 FlowSection
        return (
          <div
            style={{
              display: 'inline-block',
              border: '2px dashed #007bff',
              borderRadius: '4px',
              position: 'relative',
              padding: '2px'
            }}
            onMouseDown={(e) => {
              // 阻止事件冒泡到父容器，避免触发外层拖拽
              e.stopPropagation()
            }}
            onDragStart={(e) => {
              // 阻止嵌套容器本身被拖拽
              e.stopPropagation()
            }}
            onDragOver={(e) => {
              // 阻止外层容器处理拖拽悬停
              e.stopPropagation()
            }}
            onDrop={(e) => {
              // 阻止外层容器处理拖拽放置
              e.stopPropagation()
            }}
          >
            <FlowSection
              schema={childSchema}
              width={300}
              height={150}
              direction="row"
              wrap={true}
              gap={8}
              rowGap={6}
              columnGap={8}
              justify="flex-start"
              align="flex-start"
              onSchemaChange={(newSchema) => {
                console.log('嵌套 FlowSection schema 更新:', newSchema)
                // 更新嵌套组件的 schema
                if (onSchemaChange && schema) {
                  const updatedSchema = { ...schema }
                  if (updatedSchema.body) {
                    updatedSchema.body[childSchema.id] = newSchema
                    onSchemaChange(updatedSchema)
                  }
                }
              }}
            />
          </div>
        )
      default:
        return (
          <div
            style={{
              padding: '8px',
              backgroundColor: '#ffc107',
              border: '1px solid #fd7e14',
              borderRadius: '4px',
              minWidth: '60px'
            }}
          >
            {childSchema.component}
          </div>
        )
    }
  }

  // 处理子组件拖拽开始
  const handleChildDragStart = (e: React.DragEvent, index: number) => {
    console.log('Child drag start:', index)

    // 阻止事件冒泡，防止触发外层容器的拖拽
    e.stopPropagation()

    setDraggedIndex(index)
    setIsDragging(true)
    e.dataTransfer.effectAllowed = 'move'

    // 设置内部拖拽标识，包含容器ID以区分不同的容器
    const containerId = schema?.id || 'flow-section'
    e.dataTransfer.setData('text/plain', `internal-${containerId}-${index}`)
    e.dataTransfer.setData('application/internal-drag', 'true')
    e.dataTransfer.setData('application/container-id', containerId)

    // 创建拖拽预览图像
    const target = e.currentTarget as HTMLElement
    const rect = target.getBoundingClientRect()

    // 创建一个半透明的预览元素
    const dragImage = target.cloneNode(true) as HTMLElement
    dragImage.style.opacity = '0.8'
    dragImage.style.transform = 'rotate(5deg)'
    dragImage.style.border = '2px dashed #007bff'
    dragImage.style.backgroundColor = 'rgba(0, 123, 255, 0.1)'

    // 将预览元素添加到 body 并设置为拖拽图像
    document.body.appendChild(dragImage)
    dragImage.style.position = 'absolute'
    dragImage.style.top = '-1000px'
    dragImage.style.left = '-1000px'

    e.dataTransfer.setDragImage(dragImage, rect.width / 2, rect.height / 2)

    // 清理预览元素
    setTimeout(() => {
      if (document.body.contains(dragImage)) {
        document.body.removeChild(dragImage)
      }
    }, 0)
  }

  // 处理子组件拖拽结束
  const handleChildDragEnd = (e: React.DragEvent) => {
    setDraggedIndex(null)
    setIsDragging(false)
    setDragOverIndex(null)
    setDropPosition(null)

    // 恢复样式
    const target = e.currentTarget as HTMLElement
    target.style.opacity = '1'
  }

  // 智能计算插入位置
  const calculateInsertPosition = (mouseX: number, mouseY: number): {
    insertIndex: number
    targetIndex: number
    position: 'left' | 'right'
  } | null => {
    if (!containerRef.current || draggedIndex === null) return null

    const childElements = containerRef.current.querySelectorAll('[data-child-index]')

    if (childElements.length === 0) return { insertIndex: 0, targetIndex: 0, position: 'left' }

    let closestIndex = 0
    let closestDistance = Infinity
    let position: 'left' | 'right' = 'left'

    // 遍历所有子元素，找到最接近鼠标位置的元素
    childElements.forEach((element, index) => {
      const rect = element.getBoundingClientRect()
      const elementCenterX = rect.left + rect.width / 2
      const elementCenterY = rect.top + rect.height / 2

      // 计算鼠标到元素中心的距离
      const distance = Math.sqrt(
        Math.pow(mouseX - elementCenterX, 2) +
        Math.pow(mouseY - elementCenterY, 2)
      )

      if (distance < closestDistance) {
        closestDistance = distance
        closestIndex = index
        // 判断鼠标在元素的左边还是右边
        position = mouseX < elementCenterX ? 'left' : 'right'
      }
    })

    // 计算实际插入位置
    let insertIndex = closestIndex
    if (position === 'right') {
      insertIndex = closestIndex + 1
    }

    // 如果拖拽的元素在插入位置之前，需要调整插入位置
    if (draggedIndex < insertIndex) {
      insertIndex -= 1
    }

    return { insertIndex, targetIndex: closestIndex, position }
  }

  // 处理容器级别的拖拽悬停
  const handleContainerDragOver = (e: React.DragEvent) => {
    e.preventDefault()

    // 只处理当前容器的内部拖拽
    if (!isDragging || draggedIndex === null) {
      e.dataTransfer.dropEffect = 'copy'
      return
    }

    e.dataTransfer.dropEffect = 'move'

    const result = calculateInsertPosition(e.clientX, e.clientY)
    if (result) {
      setDragOverIndex(result.targetIndex)
      setDropPosition(result.position)
      setInsertIndex(result.insertIndex)

      console.log('Smart drag over:', result)
    }
  }

  // 处理容器拖拽放置 - 智能插入
  const handleContainerDrop = (e: React.DragEvent) => {
    e.preventDefault()

    console.log('Container drop event triggered:', { draggedIndex, isDragging })

    if (!isDragging || draggedIndex === null) {
      // 处理外部组件拖拽
      try {
        const moduleData = JSON.parse(e.dataTransfer.getData('application/json'))
        console.log('拖拽的模块到流式布局:', moduleData)

        // 创建新的组件实例
        const newComponentId = `${moduleData.component}-${Date.now()}`
        const newComponent: BaseSchema = {
          component: moduleData.component,
          id: newComponentId,
          name: moduleData.name
        }

        // 更新 schema
        const updatedSchema: BaseSchema = {
          component: schema?.component || 'FlowSection',
          id: schema?.id || 'flow-section',
          body: {
            ...schema?.body,
            [newComponentId]: newComponent
          }
        }

        onSchemaChange?.(updatedSchema)
      } catch (error) {
        console.error('处理拖拽数据失败:', error)
      }
      return
    }

    // 处理内部拖拽重排序
    const result = calculateInsertPosition(e.clientX, e.clientY)
    if (!result) return

    console.log('Smart drop:', result)

    // 插入式排序
    const newChildren = [...children]
    const draggedItem = newChildren[draggedIndex]

    console.log('Before reorder:', newChildren.map(c => c.id))

    // 移除被拖拽的项目
    newChildren.splice(draggedIndex, 1)

    // 在新位置插入
    const finalInsertIndex = Math.max(0, Math.min(result.insertIndex, newChildren.length))
    newChildren.splice(finalInsertIndex, 0, draggedItem)

    console.log('After reorder:', newChildren.map(c => c.id))

    setChildren(newChildren)
    setDragOverIndex(null)
    setDropPosition(null)
    setInsertIndex(null)
    setDraggedIndex(null)
    setIsDragging(false)

    // 更新 schema
    if (onSchemaChange && schema) {
      const newSchema = { ...schema }
      const newBody: { [key: string]: BaseSchema } = {}

      newChildren.forEach((child) => {
        newBody[child.id] = child.schema
      })

      newSchema.body = newBody
      onSchemaChange(newSchema)
      console.log('Schema updated')
    }
  }



  // 双击添加组件
  const handleDoubleClick = useCallback((e: React.MouseEvent) => {
    e.stopPropagation()

    // 根据按键决定添加的组件类型
    let componentType = 'button'
    let componentName = '新按钮'

    if (e.shiftKey) {
      componentType = 'text'
      componentName = '新文本'
    } else if (e.ctrlKey || e.metaKey) {
      componentType = 'div'
      componentName = '新容器'
    }

    const timestamp = Date.now()
    const newChildId = `${componentType}-${timestamp}`
    const newChild = {
      id: newChildId,
      schema: {
        component: componentType,
        id: newChildId,
        name: componentName
      } as BaseSchema
    }

    setChildren(prev => [...prev, newChild])

    // 如果有回调函数，更新父级schema
    if (onSchemaChange && schema) {
      const newSchema = { ...schema }
      if (!newSchema.body) newSchema.body = {}
      newSchema.body[newChild.id] = newChild.schema
      onSchemaChange(newSchema)
    }
  }, [onSchemaChange, schema])

  return (
    <>
      <style>
        {`
          @keyframes pulse {
            0% { opacity: 1; transform: scaleX(1); }
            50% { opacity: 0.7; transform: scaleX(1.2); }
            100% { opacity: 1; transform: scaleX(1); }
          }
        `}
      </style>
      <div
        ref={containerRef}
        style={{
          position: 'relative',
          width,
          height,
          border: '2px dashed #007bff',
          borderRadius: '8px',
          backgroundColor: '#f0f8ff',
          overflow: 'auto',
          cursor: 'crosshair',
          display: 'flex',
          flexDirection: direction,
          flexWrap: wrap ? 'wrap' : 'nowrap',
          rowGap: `${actualRowGap}px`,
          columnGap: `${actualColumnGap}px`,
          justifyContent: justify,
          alignItems: align,
          padding: '16px'
        }}
        onDrop={handleContainerDrop}
        onDragOver={handleContainerDragOver}
        onDoubleClick={handleDoubleClick}
      >
      {children.map((child, index) => (
        <div
          key={child.id}
          data-child-index={index}
          draggable={true}
          style={{
            cursor: isDragging && draggedIndex === index ? 'grabbing' : 'grab',
            userSelect: 'none',
            transition: draggedIndex === index ? 'none' : 'all 0.2s ease',
            opacity: draggedIndex === index ? 0.6 : 1,
            transform: draggedIndex === index ? 'rotate(2deg)' : 'rotate(0deg)',
            borderRadius: '6px',
            padding: '4px',
            position: 'relative',
            zIndex: draggedIndex === index ? 1000 : 1,
            boxShadow: draggedIndex === index ? '0 8px 20px rgba(0, 0, 0, 0.2)' : 'none'
          }}
          onDragStart={(e) => handleChildDragStart(e, index)}
          onDragEnd={handleChildDragEnd}
        >
          <div style={{ position: 'relative', display: 'inline-block' }}>
            {/* 左侧插入指示线 */}
            {dragOverIndex === index && draggedIndex !== null && draggedIndex !== index && dropPosition === 'left' && (
              <div
                style={{
                  position: 'absolute',
                  left: '-6px',
                  top: '-4px',
                  bottom: '-4px',
                  width: '4px',
                  backgroundColor: '#007bff',
                  borderRadius: '2px',
                  zIndex: 1001,
                  pointerEvents: 'none',
                  boxShadow: '0 0 12px rgba(0, 123, 255, 0.8)',
                  animation: 'pulse 1s infinite'
                }}
              />
            )}

            {/* 右侧插入指示线 */}
            {dragOverIndex === index && draggedIndex !== null && draggedIndex !== index && dropPosition === 'right' && (
              <div
                style={{
                  position: 'absolute',
                  right: '-6px',
                  top: '-4px',
                  bottom: '-4px',
                  width: '4px',
                  backgroundColor: '#007bff',
                  borderRadius: '2px',
                  zIndex: 1001,
                  pointerEvents: 'none',
                  boxShadow: '0 0 12px rgba(0, 123, 255, 0.8)',
                  animation: 'pulse 1s infinite'
                }}
              />
            )}

            {renderChild(child.schema)}
          </div>
        </div>
      ))}

      {children.length === 0 && (
        <div
          style={{
            position: 'absolute',
            top: '50%',
            left: '50%',
            transform: 'translate(-50%, -50%)',
            color: '#007bff',
            fontSize: '12px',
            pointerEvents: 'none',
            textAlign: 'center',
            lineHeight: '1.5'
          }}
        >
          流式布局容器<br/>
          双击添加按钮<br/>
          Shift+双击添加文本<br/>
          Ctrl+双击添加容器<br/>
          拖拽组件到此处<br/>
          拖拽到组件左右侧插入
        </div>
      )}
      </div>
    </>
  )
}

export default FlowSection
