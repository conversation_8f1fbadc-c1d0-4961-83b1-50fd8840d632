import { useState } from 'react'
import { AbsoluteSection, FlowSection } from 'lcanvas'
import Tree, { type TreeNode } from './component/structTree/Tree'
import type { BaseSchema } from 'nui'

const DesignerSimple = () => {
  const [schema, setSchema] = useState<BaseSchema>({
    component: 'div',
    id: '1',
    body: [
      {
        component: 'FlowSection',
        id: 'default-section',
        name: '默认区域',
        style: {
          width: '100%',
          minHeight: '300px',
          border: '2px dashed #ddd',
          borderRadius: '8px',
          backgroundColor: '#f9f9f9',
          padding: '20px',
          marginBottom: '0px'
        },
        body: []
      }
    ]
  })

  const [expandedMenu, setExpandedMenu] = useState<string | null>(null)
  const [canvasMode, setCanvasMode] = useState<'lcanvas' | 'absolute' | 'flow'>('absolute')

  // 页面树数据
  const [pageTreeData] = useState<TreeNode[]>([
    {
      id: 'app-pages',
      label: '应用页面',
      children: [
        {
          id: 'home',
          label: '首页',
          icon: '🏠',
          type: 'page',
          path: '/home',
          status: 'published'
        },
        {
          id: 'about',
          label: '关于我们',
          icon: '📋',
          type: 'page',
          path: '/about',
          status: 'draft'
        },
        {
          id: 'products',
          label: '产品中心',
          icon: '📦',
          type: 'page',
          path: '/products',
          status: 'published',
          children: [
            {
              id: 'product-list',
              label: '产品列表',
              icon: '📝',
              type: 'page',
              path: '/products/list',
              status: 'published'
            },
            {
              id: 'product-detail',
              label: '产品详情',
              icon: '🔍',
              type: 'page',
              path: '/products/detail',
              status: 'published'
            }
          ]
        },
        {
          id: 'contact',
          label: '联系我们',
          icon: '📞',
          type: 'page',
          path: '/contact',
          status: 'published'
        }
      ]
    },
    {
      id: 'system-pages',
      label: '系统页面',
      children: [
        {
          id: 'login',
          label: '登录页',
          icon: '🔐',
          type: 'system',
          path: '/login',
          status: 'published'
        },
        {
          id: 'register',
          label: '注册页',
          icon: '📝',
          type: 'system',
          path: '/register',
          status: 'published'
        },
        {
          id: '404',
          label: '404错误页',
          icon: '❌',
          type: 'system',
          path: '/404',
          status: 'published'
        },
        {
          id: 'admin',
          label: '管理后台',
          icon: '⚙️',
          type: 'system',
          path: '/admin',
          status: 'published',
          children: [
            {
              id: 'admin-dashboard',
              label: '仪表盘',
              icon: '📊',
              type: 'system',
              path: '/admin/dashboard',
              status: 'published'
            },
            {
              id: 'admin-users',
              label: '用户管理',
              icon: '👥',
              type: 'system',
              path: '/admin/users',
              status: 'published'
            },
            {
              id: 'admin-settings',
              label: '系统设置',
              icon: '⚙️',
              type: 'system',
              path: '/admin/settings',
              status: 'draft'
            }
          ]
        }
      ]
    }
  ])

  const [selectedPage, setSelectedPage] = useState<TreeNode | null>(null)
  const [pageSearchValue, setPageSearchValue] = useState('')
  const [showAddSectionButton, setShowAddSectionButton] = useState(false)

  const handleMenuClick = (menuId: string) => {
    if (expandedMenu === menuId) {
      setExpandedMenu(null)
    } else {
      setExpandedMenu(menuId)
    }
  }

  const handleSchemaChange = (newSchema: BaseSchema) => {
    console.log('Schema 更新前:', schema)
    console.log('Schema 更新后:', newSchema)
    setSchema(newSchema)
  }

  const handleAddComponent = (componentType: string, componentName: string) => {
    const newComponentId = `${componentType}-${Date.now()}`
    const newComponent: BaseSchema = {
      component: componentType,
      id: newComponentId,
      name: componentName
    }

    // 更新 schema，添加新组件到第一个区域中
    const currentBody = Array.isArray(schema.body) ? [...schema.body] : []

    if (currentBody.length > 0) {
      // 添加到第一个区域
      const firstSection = currentBody[0]
      if (Array.isArray(firstSection.body)) {
        firstSection.body = [...firstSection.body, newComponent]
      } else {
        firstSection.body = [newComponent]
      }
    } else {
      // 如果没有区域，创建一个新区域
      const newSection: BaseSchema = {
        component: 'FlowSection',
        id: `section-${Date.now()}`,
        name: '新区域',
        style: {
          width: '100%',
          minHeight: '300px',
          border: '2px dashed #ddd',
          borderRadius: '8px',
          backgroundColor: '#f9f9f9',
          padding: '20px'
        },
        body: [newComponent]
      }
      currentBody.push(newSection)
    }

    const updatedSchema: BaseSchema = {
      ...schema,
      body: currentBody
    }

    console.log('添加组件:', componentType, componentName)
    console.log('更新前 schema:', schema)
    console.log('更新后 schema:', updatedSchema)
    setSchema(updatedSchema)

    // 关闭模块面板
    setExpandedMenu(null)
  }

  const handleExportSchema = () => {
    const dataStr = JSON.stringify(schema, null, 2)
    const dataBlob = new Blob([dataStr], { type: 'application/json' })
    const url = URL.createObjectURL(dataBlob)
    const link = document.createElement('a')
    link.href = url
    link.download = 'schema.json'
    link.click()
    URL.revokeObjectURL(url)
  }

  const handleClearCanvas = () => {
    if (confirm('确定要清空画布吗？此操作不可撤销。')) {
      setSchema({
        component: 'div',
        id: '1',
        body: []
      })
    }
  }

  const handleResetPositions = () => {
    // 这个功能需要 AbsoluteSection 组件支持，暂时显示提示
    alert('重置位置功能需要在 AbsoluteSection 组件中实现')
  }

  const handlePageNodeClick = (node: TreeNode) => {
    setSelectedPage(node)
    console.log('选中页面:', node)
  }

  const handlePageSearchChange = (value: string) => {
    setPageSearchValue(value)
  }

  const handleAddNewSection = () => {
    const newSectionId = `section-${Date.now()}`
    const newSection: BaseSchema = {
      component: 'FlowSection', // 使用FlowSection作为新区域的容器
      id: newSectionId,
      name: '新区域',
      style: {
        width: '100%',
        minHeight: '300px',
        border: '2px dashed #ddd',
        borderRadius: '8px',
        backgroundColor: '#f9f9f9',
        marginTop: '0px', // 确保没有上边距，与上面的区域紧密相连
        padding: '20px',
        position: 'relative'
      },
      body: [] // 新区域内部可以包含子组件
    }

    // 更新 schema，添加新区域
    const currentBody = Array.isArray(schema.body) ? schema.body : []

    const updatedSchema: BaseSchema = {
      ...schema,
      body: [...currentBody, newSection]
    }

    console.log('添加新区域:', newSection)
    setSchema(updatedSchema)
  }

  const renderPageNode = (node: TreeNode) => {
    const getStatusColor = (status: string) => {
      switch (status) {
        case 'published': return '#28a745'
        case 'draft': return '#ffc107'
        case 'archived': return '#6c757d'
        default: return '#6c757d'
      }
    }

    const getStatusText = (status: string) => {
      switch (status) {
        case 'published': return '已发布'
        case 'draft': return '草稿'
        case 'archived': return '已归档'
        default: return '未知'
      }
    }

    return (
      <div style={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
        width: '100%'
      }}>
        <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
          <span style={{ fontSize: '14px' }}>{node.icon}</span>
          <span style={{ fontSize: '13px', color: '#333' }}>{node.label}</span>
          {node.path && (
            <span style={{
              fontSize: '11px',
              color: '#999',
              fontFamily: 'monospace'
            }}>
              {node.path}
            </span>
          )}
        </div>
        {node.status && (
          <span style={{
            fontSize: '10px',
            padding: '2px 6px',
            borderRadius: '10px',
            backgroundColor: getStatusColor(node.status),
            color: 'white'
          }}>
            {getStatusText(node.status)}
          </span>
        )}
      </div>
    )
  }

  return (
    <div style={{ 
      display: 'flex', 
      height: '100vh', 
      background: '#f5f5f5' 
    }}>
      {/* 左侧菜单 */}
      <div style={{
        width: '60px',
        background: 'linear-gradient(180deg, #ff6b6b 0%, #ee5a52 100%)',
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        padding: '10px 0',
        position: 'relative',
        zIndex: 1000
      }}>
        <div
          style={{
            color: 'white',
            fontSize: '12px',
            textAlign: 'center',
            marginBottom: '20px',
            padding: '10px 5px',
            cursor: 'pointer',
            borderRadius: '4px',
            background: expandedMenu === 'module' ? 'rgba(255, 255, 255, 0.2)' : 'transparent',
            borderLeft: expandedMenu === 'module' ? '3px solid #fff' : '3px solid transparent'
          }}
          onClick={() => handleMenuClick('module')}
        >
          🧩<br/>模块
        </div>
        <div
          style={{
            color: 'white',
            fontSize: '12px',
            textAlign: 'center',
            marginBottom: '20px',
            padding: '10px 5px',
            cursor: 'pointer',
            borderRadius: '4px',
            background: expandedMenu === 'page' ? 'rgba(255, 255, 255, 0.2)' : 'transparent',
            borderLeft: expandedMenu === 'page' ? '3px solid #fff' : '3px solid transparent'
          }}
          onClick={() => handleMenuClick('page')}
        >
          📄<br/>页面
        </div>
        <div
          style={{
            color: 'white',
            fontSize: '12px',
            textAlign: 'center',
            marginBottom: '20px',
            padding: '10px 5px',
            cursor: 'pointer',
            borderRadius: '4px',
            background: expandedMenu === 'style' ? 'rgba(255, 255, 255, 0.2)' : 'transparent',
            borderLeft: expandedMenu === 'style' ? '3px solid #fff' : '3px solid transparent'
          }}
          onClick={() => handleMenuClick('style')}
        >
          🎨<br/>风格
        </div>
      </div>

      {/* 展开的面板 */}
      {expandedMenu && (
        <div style={{
          position: 'fixed',
          left: '60px',
          top: '0',
          width: '400px',
          height: '100vh',
          background: 'white',
          boxShadow: '2px 0 12px rgba(0, 0, 0, 0.15)',
          zIndex: 1001,
          display: 'flex',
          flexDirection: 'column'
        }}>
          {/* 面板头部 */}
          <div style={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            padding: '16px 20px',
            borderBottom: '1px solid #e5e5e5',
            background: '#f8f9fa'
          }}>
            <span style={{ fontSize: '16px', fontWeight: '600', color: '#333' }}>
              {expandedMenu === 'module' && '模块管理'}
              {expandedMenu === 'page' && '页面管理'}
              {expandedMenu === 'style' && '风格管理'}
            </span>
            <button
              style={{
                background: 'none',
                border: 'none',
                fontSize: '16px',
                cursor: 'pointer',
                color: '#666',
                padding: '4px',
                borderRadius: '4px'
              }}
              onClick={() => setExpandedMenu(null)}
            >
              ✕
            </button>
          </div>

          {/* 面板内容 */}
          <div style={{ flex: 1, padding: '20px', overflow: 'auto' }}>
            {expandedMenu === 'module' && (
              <div>
                <h4 style={{ margin: '0 0 16px 0', color: '#333' }}>组件库</h4>
                <div style={{ display: 'grid', gridTemplateColumns: 'repeat(2, 1fr)', gap: '12px' }}>
                  <div
                    style={{
                      padding: '12px',
                      border: '1px solid #e5e5e5',
                      borderRadius: '6px',
                      cursor: 'pointer',
                      display: 'flex',
                      alignItems: 'center',
                      gap: '8px',
                      transition: 'all 0.2s ease'
                    }}
                    onClick={() => handleAddComponent('text', '文字')}
                    onMouseEnter={(e) => {
                      e.currentTarget.style.borderColor = '#007bff'
                      e.currentTarget.style.boxShadow = '0 2px 8px rgba(0, 123, 255, 0.15)'
                    }}
                    onMouseLeave={(e) => {
                      e.currentTarget.style.borderColor = '#e5e5e5'
                      e.currentTarget.style.boxShadow = 'none'
                    }}
                  >
                    <span style={{ fontSize: '20px' }}>📝</span>
                    <div>
                      <div style={{ fontWeight: '500', fontSize: '14px' }}>文字</div>
                      <div style={{ fontSize: '12px', color: '#666' }}>基础文本组件</div>
                    </div>
                  </div>
                  <div
                    style={{
                      padding: '12px',
                      border: '1px solid #e5e5e5',
                      borderRadius: '6px',
                      cursor: 'pointer',
                      display: 'flex',
                      alignItems: 'center',
                      gap: '8px',
                      transition: 'all 0.2s ease'
                    }}
                    onClick={() => handleAddComponent('button', '按钮')}
                    onMouseEnter={(e) => {
                      e.currentTarget.style.borderColor = '#007bff'
                      e.currentTarget.style.boxShadow = '0 2px 8px rgba(0, 123, 255, 0.15)'
                    }}
                    onMouseLeave={(e) => {
                      e.currentTarget.style.borderColor = '#e5e5e5'
                      e.currentTarget.style.boxShadow = 'none'
                    }}
                  >
                    <span style={{ fontSize: '20px' }}>🔘</span>
                    <div>
                      <div style={{ fontWeight: '500', fontSize: '14px' }}>按钮</div>
                      <div style={{ fontSize: '12px', color: '#666' }}>可点击按钮</div>
                    </div>
                  </div>
                  <div
                    style={{
                      padding: '12px',
                      border: '1px solid #e5e5e5',
                      borderRadius: '6px',
                      cursor: 'pointer',
                      display: 'flex',
                      alignItems: 'center',
                      gap: '8px',
                      transition: 'all 0.2s ease'
                    }}
                    onClick={() => handleAddComponent('div', '容器')}
                    onMouseEnter={(e) => {
                      e.currentTarget.style.borderColor = '#007bff'
                      e.currentTarget.style.boxShadow = '0 2px 8px rgba(0, 123, 255, 0.15)'
                    }}
                    onMouseLeave={(e) => {
                      e.currentTarget.style.borderColor = '#e5e5e5'
                      e.currentTarget.style.boxShadow = 'none'
                    }}
                  >
                    <span style={{ fontSize: '20px' }}>📦</span>
                    <div>
                      <div style={{ fontWeight: '500', fontSize: '14px' }}>容器</div>
                      <div style={{ fontSize: '12px', color: '#666' }}>通用容器</div>
                    </div>
                  </div>
                  <div
                    style={{
                      padding: '12px',
                      border: '1px solid #e5e5e5',
                      borderRadius: '6px',
                      cursor: 'pointer',
                      display: 'flex',
                      alignItems: 'center',
                      gap: '8px',
                      transition: 'all 0.2s ease'
                    }}
                    onClick={() => handleAddComponent('AbsoluteSection', '绝对定位容器')}
                    onMouseEnter={(e) => {
                      e.currentTarget.style.borderColor = '#007bff'
                      e.currentTarget.style.boxShadow = '0 2px 8px rgba(0, 123, 255, 0.15)'
                    }}
                    onMouseLeave={(e) => {
                      e.currentTarget.style.borderColor = '#e5e5e5'
                      e.currentTarget.style.boxShadow = 'none'
                    }}
                  >
                    <span style={{ fontSize: '20px' }}>🎯</span>
                    <div>
                      <div style={{ fontWeight: '500', fontSize: '14px' }}>绝对定位</div>
                      <div style={{ fontSize: '12px', color: '#666' }}>拖拽容器</div>
                    </div>
                  </div>
                  <div
                    style={{
                      padding: '12px',
                      border: '1px solid #e5e5e5',
                      borderRadius: '6px',
                      cursor: 'pointer',
                      display: 'flex',
                      alignItems: 'center',
                      gap: '8px',
                      transition: 'all 0.2s ease'
                    }}
                    onClick={() => handleAddComponent('FlowSection', '流式布局容器')}
                    onMouseEnter={(e) => {
                      e.currentTarget.style.borderColor = '#007bff'
                      e.currentTarget.style.boxShadow = '0 2px 8px rgba(0, 123, 255, 0.15)'
                    }}
                    onMouseLeave={(e) => {
                      e.currentTarget.style.borderColor = '#e5e5e5'
                      e.currentTarget.style.boxShadow = 'none'
                    }}
                  >
                    <span style={{ fontSize: '20px' }}>🌊</span>
                    <div>
                      <div style={{ fontWeight: '500', fontSize: '14px' }}>流式布局</div>
                      <div style={{ fontSize: '12px', color: '#666' }}>自动排列容器</div>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {expandedMenu === 'page' && (
              <div>
                <h4 style={{ margin: '0 0 16px 0', color: '#333' }}>页面管理</h4>

                {/* 页面树组件 */}
                <div style={{
                  height: 'calc(100vh - 200px)',
                  overflow: 'hidden',
                  display: 'flex',
                  flexDirection: 'column'
                }}>
                  <Tree
                    data={pageTreeData}
                    draggable={true}
                    showSearch={true}
                    searchPlaceholder="搜索页面..."
                    searchValue={pageSearchValue}
                    onSearchChange={handlePageSearchChange}
                    defaultExpandAll={true}
                    expandOnClickNode={true}
                    onNodeClick={handlePageNodeClick}
                    renderContent={renderPageNode}
                    indent={16}
                  />
                </div>

                {/* 选中页面信息 */}
                {selectedPage && (
                  <div style={{
                    marginTop: '16px',
                    padding: '12px',
                    background: '#f8f9fa',
                    border: '1px solid #e5e5e5',
                    borderRadius: '6px'
                  }}>
                    <h5 style={{
                      margin: '0 0 8px 0',
                      color: '#333',
                      fontSize: '13px',
                      fontWeight: '600'
                    }}>
                      当前选中页面
                    </h5>
                    <div style={{ fontSize: '12px', color: '#666' }}>
                      <div><strong>名称:</strong> {selectedPage.label}</div>
                      {selectedPage.path && <div><strong>路径:</strong> {selectedPage.path}</div>}
                      {selectedPage.type && <div><strong>类型:</strong> {selectedPage.type}</div>}
                      {selectedPage.status && <div><strong>状态:</strong> {selectedPage.status}</div>}
                    </div>
                    <div style={{ marginTop: '8px', display: 'flex', gap: '8px' }}>
                      <button style={{
                        padding: '4px 8px',
                        background: '#007bff',
                        color: 'white',
                        border: 'none',
                        borderRadius: '3px',
                        cursor: 'pointer',
                        fontSize: '11px'
                      }}>
                        编辑页面
                      </button>
                      <button style={{
                        padding: '4px 8px',
                        background: '#28a745',
                        color: 'white',
                        border: 'none',
                        borderRadius: '3px',
                        cursor: 'pointer',
                        fontSize: '11px'
                      }}>
                        预览页面
                      </button>
                    </div>
                  </div>
                )}
              </div>
            )}

            {expandedMenu === 'style' && (
              <div>
                <h4 style={{ margin: '0 0 16px 0', color: '#333' }}>风格设置</h4>
                <p style={{ color: '#666', fontSize: '14px' }}>风格管理功能开发中...</p>
              </div>
            )}
          </div>
        </div>
      )}

      {/* 主要内容区域 */}
      <div style={{ 
        flex: 1, 
        display: 'flex', 
        flexDirection: 'column',
        marginLeft: '0'
      }}>
        {/* 工具栏 */}
        <div style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          padding: '12px 20px',
          background: 'white',
          borderBottom: '1px solid #e5e5e5',
          boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)'
        }}>
          <div style={{ display: 'flex', gap: '8px' }}>
            <button
              style={{
                padding: '8px 16px',
                border: '1px solid #ddd',
                background: canvasMode === 'lcanvas' ? '#007bff' : 'white',
                color: canvasMode === 'lcanvas' ? 'white' : '#666',
                borderRadius: '4px',
                cursor: 'pointer'
              }}
              onClick={() => setCanvasMode('lcanvas')}
            >
              LCanvas 模式
            </button>
            <button
              style={{
                padding: '8px 16px',
                border: '1px solid #ddd',
                background: canvasMode === 'absolute' ? '#007bff' : 'white',
                color: canvasMode === 'absolute' ? 'white' : '#666',
                borderRadius: '4px',
                cursor: 'pointer'
              }}
              onClick={() => setCanvasMode('absolute')}
            >
              绝对定位模式
            </button>
            <button
              style={{
                padding: '8px 16px',
                border: '1px solid #ddd',
                background: canvasMode === 'flow' ? '#007bff' : 'white',
                color: canvasMode === 'flow' ? 'white' : '#666',
                borderRadius: '4px',
                cursor: 'pointer'
              }}
              onClick={() => setCanvasMode('flow')}
            >
              流式布局模式
            </button>
          </div>
          <button
            style={{
              padding: '8px 16px',
              background: '#28a745',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              cursor: 'pointer'
            }}
            onClick={() => handleMenuClick('module')}
          >
            添加组件
          </button>
        </div>

        {/* 画布区域 */}
        <div
          style={{
            flex: 1,
            padding: '20px',
            background: 'white',
            margin: '20px',
            borderRadius: '8px',
            boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)',
            overflow: 'hidden',
            position: 'relative'
          }}
          onMouseEnter={() => setShowAddSectionButton(true)}
          onMouseLeave={() => setShowAddSectionButton(false)}
        >
          {/* 渲染多个区域 */}
            <div style={{
              minHeight: '500px',
              display: 'flex',
              flexDirection: 'column',
              gap: '0px', // 确保区域之间没有间隙
              width: '100%'
            }}>
              {Array.isArray(schema.body) && schema.body.length > 0 ? (
                schema.body.map((section, index) => {
                  // 根据区域类型渲染不同的组件
                  const handleSectionChange = (newSectionSchema: BaseSchema) => {
                    const updatedBody = [...(Array.isArray(schema.body) ? schema.body : [])]
                    updatedBody[index] = newSectionSchema
                    setSchema({
                      ...schema,
                      body: updatedBody
                    })
                  }

                  return (
                    <div key={section.id} style={{
                      width: '100%',
                      border: '2px dashed #ddd',
                      borderRadius: index === 0 ? '8px 8px 0 0' : index === (Array.isArray(schema.body) ? schema.body.length - 1 : 0) ? '0 0 8px 8px' : '0',
                      borderTop: index === 0 ? '2px dashed #ddd' : '1px dashed #ddd',
                      borderBottom: index === (Array.isArray(schema.body) ? schema.body.length - 1 : 0) ? '2px dashed #ddd' : '1px dashed #ddd',
                      borderLeft: '2px dashed #ddd',
                      borderRight: '2px dashed #ddd',
                      minHeight: '200px',
                      backgroundColor: '#f9f9f9',
                      position: 'relative',
                      marginTop: '0px',
                      marginBottom: '0px'
                    }}>
                      {section.component === 'AbsoluteSection' ? (
                        <AbsoluteSection
                          schema={section}
                          width="100%"
                          height="200px"
                          onSchemaChange={handleSectionChange}
                        />
                      ) : section.component === 'FlowSection' ? (
                        <FlowSection
                          schema={section}
                          width="100%"
                          height="200px"
                          direction="row"
                          wrap={true}
                          gap={12}
                          rowGap={8}
                          columnGap={12}
                          justify="flex-start"
                          align="flex-start"
                          onSchemaChange={handleSectionChange}
                        />
                      ) : (
                        <div style={{
                          width: '100%',
                          height: '100%',
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          color: '#999',
                          fontSize: '14px'
                        }}>
                          {section.name || `区域 ${index + 1}`}
                          {Array.isArray(section.body) && section.body.length > 0 && (
                            <div style={{
                              position: 'absolute',
                              top: '10px',
                              right: '10px',
                              fontSize: '12px',
                              background: '#007bff',
                              color: 'white',
                              padding: '2px 6px',
                              borderRadius: '10px'
                            }}>
                              {section.body.length} 个组件
                            </div>
                          )}
                        </div>
                      )}
                    </div>
                  )
                })
              ) : (
                <div style={{
                  border: '2px dashed #ddd',
                  borderRadius: '8px',
                  height: '300px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  color: '#999',
                  fontSize: '14px'
                }}>
                  点击下方 + 号添加新区域，或点击左侧组件添加到画布
                </div>
              )}

              {/* 底部增加区域按钮 */}
              {showAddSectionButton && (
                <div style={{
                  width: '100%',
                  display: 'flex',
                  justifyContent: 'center',
                  marginTop: '10px',
                  zIndex: 1000
                }}>
              <button
                style={{
                  width: '40px',
                  height: '40px',
                  borderRadius: '50%',
                  border: '2px solid #007bff',
                  background: 'white',
                  color: '#007bff',
                  fontSize: '20px',
                  cursor: 'pointer',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  boxShadow: '0 2px 8px rgba(0, 123, 255, 0.3)',
                  transition: 'all 0.2s ease'
                }}
                onClick={handleAddNewSection}
                onMouseEnter={(e) => {
                  e.currentTarget.style.background = '#007bff'
                  e.currentTarget.style.color = 'white'
                  e.currentTarget.style.transform = 'scale(1.1)'
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.background = 'white'
                  e.currentTarget.style.color = '#007bff'
                  e.currentTarget.style.transform = 'scale(1)'
                }}
                title="添加新区域"
              >
                +
              </button>
                </div>
              )}
            </div>
        </div>
      </div>

      {/* 右侧属性面板 */}
      <div style={{
        width: '320px',
        height: '100vh',
        background: 'white',
        borderLeft: '1px solid #e5e5e5',
        boxShadow: '-2px 0 8px rgba(0, 0, 0, 0.1)',
        display: 'flex',
        flexDirection: 'column',
        overflow: 'hidden'
      }}>
        {/* 面板头部 */}
        <div style={{
          padding: '16px 20px',
          borderBottom: '1px solid #e5e5e5',
          background: '#f8f9fa'
        }}>
          <h4 style={{ margin: '0', color: '#333', fontSize: '16px', fontWeight: '600' }}>
            属性面板
          </h4>
        </div>

        {/* 面板内容 */}
        <div style={{
          flex: 1,
          padding: '20px',
          overflow: 'auto'
        }}>
          {/* Schema 信息 */}
          <div style={{ marginBottom: '20px' }}>
            <h5 style={{
              margin: '0 0 12px 0',
              color: '#666',
              fontSize: '14px',
              fontWeight: '500',
              display: 'flex',
              alignItems: 'center',
              gap: '8px'
            }}>
              📋 Schema 结构
              <span style={{
                background: '#007bff',
                color: 'white',
                fontSize: '10px',
                padding: '2px 6px',
                borderRadius: '10px'
              }}>
                {Array.isArray(schema.body) ? schema.body.length : 0} 个区域
              </span>
            </h5>
            <div style={{
              background: '#f8f9fa',
              border: '1px solid #e5e5e5',
              borderRadius: '6px',
              overflow: 'hidden'
            }}>
              <pre style={{
                margin: '0',
                padding: '16px',
                fontSize: '11px',
                lineHeight: '1.5',
                color: '#333',
                fontFamily: 'Monaco, Menlo, "Ubuntu Mono", monospace',
                overflow: 'auto',
                maxHeight: '400px',
                whiteSpace: 'pre-wrap',
                wordBreak: 'break-word'
              }}>
                {JSON.stringify(schema, null, 2)}
              </pre>
            </div>
          </div>

          {/* 组件统计 */}
          <div style={{ marginBottom: '20px' }}>
            <h5 style={{
              margin: '0 0 12px 0',
              color: '#666',
              fontSize: '14px',
              fontWeight: '500'
            }}>
              📊 组件统计
            </h5>
            <div style={{
              background: '#f8f9fa',
              border: '1px solid #e5e5e5',
              borderRadius: '6px',
              padding: '12px'
            }}>
              {Array.isArray(schema.body) && schema.body.length > 0 ? (
                <div>
                  {Object.entries(
                    schema.body.reduce((acc: Record<string, number>, item) => {
                      acc[item.component] = (acc[item.component] || 0) + 1
                      return acc
                    }, {})
                  ).map(([component, count]) => (
                    <div key={component} style={{
                      display: 'flex',
                      justifyContent: 'space-between',
                      alignItems: 'center',
                      padding: '4px 0',
                      fontSize: '12px'
                    }}>
                      <span style={{ color: '#333' }}>
                        {component === 'button' && '🔘 按钮'}
                        {component === 'text' && '📝 文字'}
                        {component === 'div' && '📦 容器'}
                        {component === 'AbsoluteSection' && '🎯 绝对定位'}
                        {component === 'FlowSection' && '🌊 流式布局'}
                        {!['button', 'text', 'div', 'AbsoluteSection', 'FlowSection'].includes(component) && `🔧 ${component}`}
                      </span>
                      <span style={{
                        background: '#007bff',
                        color: 'white',
                        fontSize: '10px',
                        padding: '2px 6px',
                        borderRadius: '10px',
                        minWidth: '20px',
                        textAlign: 'center'
                      }}>
                        {count}
                      </span>
                    </div>
                  ))}
                </div>
              ) : (
                <div style={{
                  color: '#999',
                  fontSize: '12px',
                  textAlign: 'center',
                  padding: '20px 0'
                }}>
                  暂无组件
                </div>
              )}
            </div>
          </div>

          {/* 操作按钮 */}
          <div>
            <h5 style={{
              margin: '0 0 12px 0',
              color: '#666',
              fontSize: '14px',
              fontWeight: '500'
            }}>
              🛠️ 操作
            </h5>
            <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
              <button
                style={{
                  padding: '8px 12px',
                  background: '#28a745',
                  color: 'white',
                  border: 'none',
                  borderRadius: '4px',
                  cursor: 'pointer',
                  fontSize: '12px',
                  transition: 'background 0.2s ease'
                }}
                onClick={handleExportSchema}
                onMouseEnter={(e) => e.currentTarget.style.background = '#218838'}
                onMouseLeave={(e) => e.currentTarget.style.background = '#28a745'}
              >
                📥 导出 Schema
              </button>
              <button
                style={{
                  padding: '8px 12px',
                  background: '#dc3545',
                  color: 'white',
                  border: 'none',
                  borderRadius: '4px',
                  cursor: 'pointer',
                  fontSize: '12px',
                  transition: 'background 0.2s ease'
                }}
                onClick={handleClearCanvas}
                onMouseEnter={(e) => e.currentTarget.style.background = '#c82333'}
                onMouseLeave={(e) => e.currentTarget.style.background = '#dc3545'}
              >
                🗑️ 清空画布
              </button>
              <button
                style={{
                  padding: '8px 12px',
                  background: '#6c757d',
                  color: 'white',
                  border: 'none',
                  borderRadius: '4px',
                  cursor: 'pointer',
                  fontSize: '12px',
                  transition: 'background 0.2s ease'
                }}
                onClick={handleResetPositions}
                onMouseEnter={(e) => e.currentTarget.style.background = '#5a6268'}
                onMouseLeave={(e) => e.currentTarget.style.background = '#6c757d'}
              >
                🔄 重置位置
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default DesignerSimple
